#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SYSTEM STARTUP AUTOMATION SETUP
# =============================================================================
# Version: 1.0.0
# Description: Configure complete system startup automation with PM2 and systemd
# Usage: ./setup-system-startup.sh [production|development]
# 
# Features:
# - PM2 startup integration with systemd
# - Service dependencies (PostgreSQL → NGINX → PM2 → Application)
# - Health check validation
# - Automatic restart policies
# - Complete stack orchestration
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${1:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Environment-specific paths
if [[ "$DEPLOYMENT_ENV" == "production" ]]; then
    APP_DIR="/home/<USER>/hauling-qr-trip-management"
    PM2_USER="ubuntu"
    SERVICE_NAME="hauling-qr-system"
    NODE_PATH="/home/<USER>/.nvm/versions/node/v18.20.4/bin"
else
    APP_DIR="$PROJECT_ROOT"
    PM2_USER="$USER"
    SERVICE_NAME="hauling-qr-system-dev"
    NODE_PATH="$(dirname $(which node))"
fi

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# =============================================================================
# PREREQUISITE CHECKS
# =============================================================================
check_prerequisites() {
    log_info "Checking prerequisites for system startup automation..."
    
    # Check if running as correct user
    if [[ "$DEPLOYMENT_ENV" == "production" && "$USER" != "ubuntu" ]]; then
        log_error "Production setup must be run as ubuntu user"
        exit 1
    fi
    
    # Check required services
    local required_services=("postgresql" "nginx")
    for service in "${required_services[@]}"; do
        if ! systemctl is-enabled "$service" &>/dev/null; then
            log_error "Required service not enabled: $service"
            log_info "Please enable with: sudo systemctl enable $service"
            exit 1
        fi
    done
    
    # Check PM2 installation
    if ! command -v pm2 &>/dev/null; then
        log_error "PM2 not found. Please install PM2 first."
        exit 1
    fi
    
    # Check application directory
    if [[ ! -d "$APP_DIR" ]]; then
        log_error "Application directory not found: $APP_DIR"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# =============================================================================
# PM2 STARTUP CONFIGURATION
# =============================================================================
setup_pm2_startup() {
    log_info "Setting up PM2 startup integration..."
    
    # Generate PM2 startup script for systemd
    local startup_output
    startup_output=$(pm2 startup systemd -u "$PM2_USER" --hp "$(eval echo ~$PM2_USER)" 2>&1)
    
    # Extract and execute the generated command
    local startup_command
    startup_command=$(echo "$startup_output" | grep -E "sudo env PATH=" | head -1)
    
    if [[ -n "$startup_command" ]]; then
        log_info "Executing PM2 startup command..."
        eval "$startup_command"
        log_success "PM2 startup script installed"
    else
        log_warning "PM2 startup command not found, may already be configured"
    fi
    
    # Start application with PM2 if not already running
    cd "$APP_DIR"
    
    if ! pm2 list | grep -q "hauling-qr-server"; then
        log_info "Starting application with PM2..."
        pm2 start ecosystem.config.js --env "$DEPLOYMENT_ENV"
    else
        log_info "Reloading existing PM2 application..."
        pm2 reload ecosystem.config.js --env "$DEPLOYMENT_ENV"
    fi
    
    # Save PM2 process list for resurrection
    pm2 save
    log_success "PM2 startup configuration completed"
}

# =============================================================================
# SYSTEMD SERVICE CONFIGURATION
# =============================================================================
create_systemd_service() {
    log_info "Creating systemd service for complete stack management..."
    
    local service_file="/etc/systemd/system/$SERVICE_NAME.service"
    
    # Create systemd service file
    sudo tee "$service_file" > /dev/null <<EOF
[Unit]
Description=Hauling QR Trip Management System
Documentation=https://github.com/mightybadz18/hauling-qr-trip-management
After=network.target postgresql.service nginx.service
Wants=postgresql.service nginx.service
Requires=network.target

[Service]
Type=oneshot
RemainAfterExit=yes
User=$PM2_USER
Group=$PM2_USER
WorkingDirectory=$APP_DIR
Environment=NODE_ENV=$DEPLOYMENT_ENV
Environment=PATH=/usr/bin:/usr/local/bin:$NODE_PATH
Environment=PM2_HOME=$(eval echo ~$PM2_USER)/.pm2

# Pre-start health checks
ExecStartPre=/bin/bash -c 'echo "Starting Hauling QR Trip System..."'
ExecStartPre=/bin/bash -c 'systemctl is-active --quiet postgresql || (echo "PostgreSQL not running" && exit 1)'
ExecStartPre=/bin/bash -c 'systemctl is-active --quiet nginx || (echo "NGINX not running" && exit 1)'
ExecStartPre=/bin/bash -c 'pg_isready -h localhost -p 5432 -d hauling_qr_system || (echo "Database not accessible" && exit 1)'

# Start PM2 processes
ExecStart=/bin/bash -c 'cd $APP_DIR && $NODE_PATH/pm2 resurrect'

# Health check after start
ExecStartPost=/bin/bash -c 'sleep 15 && curl -f http://localhost:8080/api/health || (echo "Health check failed" && exit 1)'

# Stop command
ExecStop=/bin/bash -c '$NODE_PATH/pm2 save && $NODE_PATH/pm2 kill'

# Restart configuration
Restart=on-failure
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR $(eval echo ~$PM2_USER)/.pm2 /tmp

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable service
    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"
    
    log_success "Systemd service created and enabled: $SERVICE_NAME"
}

# =============================================================================
# HEALTH CHECK SCRIPT
# =============================================================================
create_health_check_script() {
    log_info "Creating system health check script..."
    
    local health_script="$APP_DIR/scripts/health-check.sh"
    mkdir -p "$(dirname "$health_script")"
    
    cat > "$health_script" <<'EOF'
#!/bin/bash
# System Health Check Script for Hauling QR Trip System

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuration
HEALTH_URL="http://localhost:8080/api/health"
DB_HEALTH_URL="http://localhost:8080/api/health/db"
CORS_TEST_URL="http://localhost:8080/cors-test"

# Check functions
check_postgresql() {
    if systemctl is-active --quiet postgresql; then
        echo -e "${GREEN}✓${NC} PostgreSQL is running"
        return 0
    else
        echo -e "${RED}✗${NC} PostgreSQL is not running"
        return 1
    fi
}

check_nginx() {
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}✓${NC} NGINX is running"
        return 0
    else
        echo -e "${RED}✗${NC} NGINX is not running"
        return 1
    fi
}

check_pm2() {
    if pm2 list | grep -q "hauling-qr-server.*online"; then
        echo -e "${GREEN}✓${NC} PM2 application is running"
        return 0
    else
        echo -e "${RED}✗${NC} PM2 application is not running"
        return 1
    fi
}

check_application_health() {
    if curl -f -s "$HEALTH_URL" > /dev/null; then
        echo -e "${GREEN}✓${NC} Application health check passed"
        return 0
    else
        echo -e "${RED}✗${NC} Application health check failed"
        return 1
    fi
}

check_database_health() {
    if curl -f -s "$DB_HEALTH_URL" > /dev/null; then
        echo -e "${GREEN}✓${NC} Database health check passed"
        return 0
    else
        echo -e "${RED}✗${NC} Database health check failed"
        return 1
    fi
}

check_cors() {
    if curl -f -s -H "Origin: https://truckhaul.top" "$CORS_TEST_URL" > /dev/null; then
        echo -e "${GREEN}✓${NC} CORS test passed"
        return 0
    else
        echo -e "${RED}✗${NC} CORS test failed"
        return 1
    fi
}

# Main health check
main() {
    echo "=== Hauling QR Trip System Health Check ==="
    echo "Timestamp: $(date)"
    echo ""
    
    local failed=0
    
    check_postgresql || ((failed++))
    check_nginx || ((failed++))
    check_pm2 || ((failed++))
    check_application_health || ((failed++))
    check_database_health || ((failed++))
    check_cors || ((failed++))
    
    echo ""
    if [[ $failed -eq 0 ]]; then
        echo -e "${GREEN}✓ All health checks passed${NC}"
        exit 0
    else
        echo -e "${RED}✗ $failed health check(s) failed${NC}"
        exit 1
    fi
}

main "$@"
EOF
    
    chmod +x "$health_script"
    log_success "Health check script created: $health_script"
}

# =============================================================================
# STARTUP VALIDATION
# =============================================================================
validate_startup_configuration() {
    log_info "Validating startup configuration..."
    
    # Test systemd service
    if sudo systemctl start "$SERVICE_NAME"; then
        log_success "Systemd service started successfully"
        
        # Wait for startup
        sleep 20
        
        # Run health check
        if [[ -f "$APP_DIR/scripts/health-check.sh" ]]; then
            if "$APP_DIR/scripts/health-check.sh"; then
                log_success "Health check validation passed"
            else
                log_error "Health check validation failed"
                return 1
            fi
        fi
        
        # Stop service for now (will be managed by systemd on boot)
        sudo systemctl stop "$SERVICE_NAME"
        
    else
        log_error "Systemd service failed to start"
        return 1
    fi
    
    log_success "Startup configuration validation completed"
}

# =============================================================================
# MAIN FUNCTION
# =============================================================================
main() {
    log_info "Setting up system startup automation for Hauling QR Trip System"
    log_info "Environment: $DEPLOYMENT_ENV"
    log_info "Application Directory: $APP_DIR"
    log_info "PM2 User: $PM2_USER"
    log_info "Service Name: $SERVICE_NAME"
    
    check_prerequisites
    setup_pm2_startup
    create_systemd_service
    create_health_check_script
    validate_startup_configuration
    
    log_success "🎉 System startup automation setup completed!"
    log_info ""
    log_info "📋 System startup sequence:"
    log_info "   1. PostgreSQL service starts"
    log_info "   2. NGINX service starts"
    log_info "   3. PM2 resurrects saved processes"
    log_info "   4. Application health checks validate startup"
    log_info ""
    log_info "🔧 Management commands:"
    log_info "   • Start system: sudo systemctl start $SERVICE_NAME"
    log_info "   • Stop system: sudo systemctl stop $SERVICE_NAME"
    log_info "   • Check status: sudo systemctl status $SERVICE_NAME"
    log_info "   • View logs: sudo journalctl -u $SERVICE_NAME -f"
    log_info "   • Health check: $APP_DIR/scripts/health-check.sh"
    log_info ""
    log_info "🚀 The system will now automatically start after VPS reboots!"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
