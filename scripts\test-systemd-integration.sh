#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SYSTEMD INTEGRATION VALIDATION SCRIPT
# =============================================================================
# Version: 1.0.0
# Description: Comprehensive validation of systemd service integration for production VPS
# Usage: ./test-systemd-integration.sh [--production] [--rollback-on-failure]
# 
# Target: Ubuntu 24.04 VPS at truckhaul.top (IP: **************)
# User: ubuntu
# Application Path: /home/<USER>/hauling-qr-trip-management
# 
# Features:
# - Systemd service file validation
# - Service installation and enablement testing
# - PM2 startup integration verification
# - Service dependency testing (PostgreSQL → NGINX → PM2)
# - Health check validation
# - Auto-restart functionality testing
# - Rollback procedures on failure
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PRODUCTION_MODE="${1:-false}"
ROLLBACK_ON_FAILURE="${2:-false}"

# Test configuration
SERVICE_NAME="hauling-qr-system"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
TEMPLATE_FILE="${PROJECT_ROOT}/deploy-hauling-qr-ubuntu/templates/${SERVICE_NAME}.service"
APP_DIR="/home/<USER>/hauling-qr-trip-management"
PM2_HOME="/home/<USER>/.pm2"
TEST_LOG="/tmp/systemd-integration-test-$(date +%Y%m%d-%H%M%S).log"

# Health check URLs
HEALTH_URL="http://localhost:8080/api/health"
DB_HEALTH_URL="http://localhost:8080/api/health/db"
CORS_TEST_URL="http://localhost:8080/cors-test"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$TEST_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$TEST_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$TEST_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$TEST_LOG"
}

log_header() {
    echo -e "${CYAN}=== $1 ===${NC}" | tee -a "$TEST_LOG"
}

# Test result functions
test_passed() {
    local test_name="$1"
    TESTS_PASSED=$((TESTS_PASSED + 1))
    log_success "✅ PASSED: $test_name"
}

test_failed() {
    local test_name="$1"
    local error_msg="${2:-No error message provided}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
    FAILED_TESTS+=("$test_name: $error_msg")
    log_error "❌ FAILED: $test_name - $error_msg"
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================
validate_environment() {
    log_header "Environment Validation"
    
    # Check if running on correct system
    if [[ "$PRODUCTION_MODE" == "--production" ]]; then
        log_info "Running in production mode validation"
        
        # Check hostname
        local hostname=$(hostname)
        if [[ "$hostname" == *"vps"* ]] || [[ "$hostname" == *"truckhaul"* ]]; then
            test_passed "Production hostname validation"
        else
            test_failed "Production hostname validation" "Expected VPS hostname, got: $hostname"
        fi
        
        # Check if ubuntu user exists
        if id ubuntu >/dev/null 2>&1; then
            test_passed "Ubuntu user exists"
        else
            test_failed "Ubuntu user exists" "Ubuntu user not found"
        fi
        
        # Check application directory
        if [[ -d "$APP_DIR" ]]; then
            test_passed "Application directory exists"
        else
            test_failed "Application directory exists" "Directory not found: $APP_DIR"
        fi
    else
        log_info "Running in development/test mode"
        test_passed "Development mode validation"
    fi
    
    # Check required commands
    local required_commands=("systemctl" "pm2" "curl" "sudo")
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            test_passed "Command available: $cmd"
        else
            test_failed "Command available: $cmd" "Command not found"
        fi
    done
}

validate_template_content() {
    log_header "Systemd Service Template Validation"
    
    # Check if template file exists
    if [[ -f "$TEMPLATE_FILE" ]]; then
        test_passed "Template file exists"
    else
        test_failed "Template file exists" "Template not found: $TEMPLATE_FILE"
        return 1
    fi
    
    # Validate template content
    local required_sections=("[Unit]" "[Service]" "[Install]")
    for section in "${required_sections[@]}"; do
        if grep -q "^$section" "$TEMPLATE_FILE"; then
            test_passed "Template contains section: $section"
        else
            test_failed "Template contains section: $section" "Section not found"
        fi
    done
    
    # Check critical configuration
    local critical_configs=(
        "User=ubuntu"
        "Group=ubuntu"
        "WorkingDirectory=/home/<USER>/hauling-qr-trip-management"
        "Environment=PM2_HOME=/home/<USER>/.pm2"
        "After=network.target postgresql.service nginx.service"
    )
    
    for config in "${critical_configs[@]}"; do
        if grep -q "$config" "$TEMPLATE_FILE"; then
            test_passed "Template contains: $config"
        else
            test_failed "Template contains: $config" "Configuration not found"
        fi
    done
}

validate_service_installation() {
    log_header "Systemd Service Installation Validation"
    
    # Check if service file exists in system location
    if [[ -f "$SERVICE_FILE" ]]; then
        test_passed "Service file installed at system location"
    else
        test_failed "Service file installed at system location" "File not found: $SERVICE_FILE"
        return 1
    fi
    
    # Check file permissions
    local file_perms=$(stat -c "%a" "$SERVICE_FILE" 2>/dev/null || echo "000")
    if [[ "$file_perms" == "644" ]]; then
        test_passed "Service file has correct permissions (644)"
    else
        test_failed "Service file has correct permissions" "Expected 644, got: $file_perms"
    fi
    
    # Check file ownership
    local file_owner=$(stat -c "%U:%G" "$SERVICE_FILE" 2>/dev/null || echo "unknown:unknown")
    if [[ "$file_owner" == "root:root" ]]; then
        test_passed "Service file has correct ownership (root:root)"
    else
        test_failed "Service file has correct ownership" "Expected root:root, got: $file_owner"
    fi
    
    # Validate systemd can parse the service file
    if sudo systemctl status "$SERVICE_NAME" >/dev/null 2>&1 || [[ $? -eq 3 ]]; then
        test_passed "Systemd can parse service file"
    else
        test_failed "Systemd can parse service file" "Systemctl status failed"
    fi
}

validate_service_enablement() {
    log_header "Systemd Service Enablement Validation"
    
    # Check if service is enabled
    if sudo systemctl is-enabled "$SERVICE_NAME" >/dev/null 2>&1; then
        test_passed "Service is enabled for auto-start"
    else
        test_failed "Service is enabled for auto-start" "Service not enabled"
    fi
    
    # Check service can be started
    log_info "Testing service start capability..."
    if sudo systemctl start "$SERVICE_NAME" 2>/dev/null; then
        test_passed "Service can be started"
        
        # Wait for startup
        sleep 15
        
        # Check if service is active
        if sudo systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
            test_passed "Service is active after start"
        else
            test_failed "Service is active after start" "Service not active"
        fi
        
        # Stop service for further testing
        sudo systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    else
        test_failed "Service can be started" "Failed to start service"
    fi
}

validate_pm2_integration() {
    log_header "PM2 Integration Validation"
    
    # Check PM2_HOME environment variable
    if [[ -d "$PM2_HOME" ]]; then
        test_passed "PM2_HOME directory exists"
    else
        test_failed "PM2_HOME directory exists" "Directory not found: $PM2_HOME"
    fi
    
    # Check PM2 startup configuration
    local startup_script="/etc/systemd/system/pm2-ubuntu.service"
    if [[ -f "$startup_script" ]] || pm2 startup 2>&1 | grep -q "already"; then
        test_passed "PM2 startup is configured"
    else
        test_failed "PM2 startup is configured" "PM2 startup not configured for ubuntu user"
    fi
    
    # Check if PM2 dump file exists (saved processes)
    if [[ -f "$PM2_HOME/dump.pm2" ]]; then
        test_passed "PM2 process list is saved"
    else
        test_failed "PM2 process list is saved" "No saved PM2 processes found"
    fi
    
    # Check PM2 ownership
    local pm2_owner=$(stat -c "%U" "$PM2_HOME" 2>/dev/null || echo "unknown")
    if [[ "$pm2_owner" == "ubuntu" ]]; then
        test_passed "PM2_HOME has correct ownership (ubuntu)"
    else
        test_failed "PM2_HOME has correct ownership" "Expected ubuntu, got: $pm2_owner"
    fi
}

validate_service_dependencies() {
    log_header "Service Dependencies Validation"
    
    # Check required services
    local required_services=("postgresql" "nginx")
    for service in "${required_services[@]}"; do
        if sudo systemctl is-enabled "$service" >/dev/null 2>&1; then
            test_passed "Required service enabled: $service"
        else
            test_failed "Required service enabled: $service" "Service not enabled"
        fi
        
        if sudo systemctl is-active "$service" >/dev/null 2>&1; then
            test_passed "Required service active: $service"
        else
            test_failed "Required service active: $service" "Service not active"
        fi
    done
    
    # Test database connectivity
    if pg_isready -h localhost -p 5432 -U hauling_app -d hauling_qr_system >/dev/null 2>&1; then
        test_passed "Database connectivity"
    else
        test_failed "Database connectivity" "Cannot connect to PostgreSQL database"
    fi
}

validate_health_checks() {
    log_header "Health Check Validation"
    
    # Start the service for health checks
    log_info "Starting service for health check validation..."
    sudo systemctl start "$SERVICE_NAME" 2>/dev/null || true
    
    # Wait for startup
    sleep 20
    
    # Test application health endpoint
    local max_attempts=6
    local attempt=1
    local health_passed=false
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts..."
        
        if curl -f -s "$HEALTH_URL" >/dev/null 2>&1; then
            test_passed "Application health endpoint responding"
            health_passed=true
            break
        fi
        
        sleep 5
        ((attempt++))
    done
    
    if [[ "$health_passed" == "false" ]]; then
        test_failed "Application health endpoint responding" "Health endpoint not accessible after $max_attempts attempts"
    fi
    
    # Test database health endpoint
    if curl -f -s "$DB_HEALTH_URL" >/dev/null 2>&1; then
        test_passed "Database health endpoint responding"
    else
        test_failed "Database health endpoint responding" "Database health endpoint not accessible"
    fi
    
    # Test CORS functionality
    if curl -f -s -H "Origin: https://truckhaul.top" "$CORS_TEST_URL" >/dev/null 2>&1; then
        test_passed "CORS functionality working"
    else
        test_failed "CORS functionality working" "CORS test failed"
    fi
    
    # Stop service after health checks
    sudo systemctl stop "$SERVICE_NAME" 2>/dev/null || true
}

validate_auto_restart_functionality() {
    log_header "Auto-Restart Functionality Validation"

    # This test simulates system reboot behavior
    log_info "Testing auto-restart functionality (simulated reboot scenario)..."

    # Ensure service is enabled
    if ! sudo systemctl is-enabled "$SERVICE_NAME" >/dev/null 2>&1; then
        test_failed "Auto-restart prerequisite" "Service must be enabled for auto-restart testing"
        return 1
    fi

    # Stop all PM2 processes to simulate clean state
    log_info "Stopping all PM2 processes to simulate clean boot state..."
    pm2 kill >/dev/null 2>&1 || true

    # Start the systemd service (simulates boot-time startup)
    log_info "Starting systemd service (simulating boot-time startup)..."
    if sudo systemctl start "$SERVICE_NAME"; then
        test_passed "Systemd service starts successfully"
    else
        test_failed "Systemd service starts successfully" "Service failed to start"
        return 1
    fi

    # Wait for full startup
    log_info "Waiting for complete application startup..."
    sleep 30

    # Check if PM2 processes are running
    if pm2 list | grep -q "hauling-qr-server.*online"; then
        test_passed "PM2 processes started via systemd service"
    else
        test_failed "PM2 processes started via systemd service" "PM2 processes not running"
    fi

    # Test application responsiveness
    local max_attempts=5
    local attempt=1
    local app_responsive=false

    while [[ $attempt -le $max_attempts ]]; do
        log_info "Testing application responsiveness (attempt $attempt/$max_attempts)..."

        if curl -f -s "$HEALTH_URL" >/dev/null 2>&1; then
            test_passed "Application responsive after systemd startup"
            app_responsive=true
            break
        fi

        sleep 10
        ((attempt++))
    done

    if [[ "$app_responsive" == "false" ]]; then
        test_failed "Application responsive after systemd startup" "Application not responding after $max_attempts attempts"
    fi

    # Test service restart capability
    log_info "Testing service restart capability..."
    if sudo systemctl restart "$SERVICE_NAME"; then
        test_passed "Service restart capability"

        # Wait for restart
        sleep 20

        # Check if still responsive after restart
        if curl -f -s "$HEALTH_URL" >/dev/null 2>&1; then
            test_passed "Application responsive after service restart"
        else
            test_failed "Application responsive after service restart" "Application not responding after restart"
        fi
    else
        test_failed "Service restart capability" "Service restart failed"
    fi

    # Clean up - stop service
    sudo systemctl stop "$SERVICE_NAME" 2>/dev/null || true
}

# =============================================================================
# MAIN VALIDATION FUNCTION
# =============================================================================
main() {
    log_header "Systemd Integration Validation"
    log_info "Starting comprehensive systemd service integration validation"
    log_info "Production Mode: $PRODUCTION_MODE"
    log_info "Rollback on Failure: $ROLLBACK_ON_FAILURE"
    log_info "Test Log: $TEST_LOG"
    log_info "Timestamp: $(date)"
    echo ""
    
    # Run all validation tests
    validate_environment
    validate_template_content
    validate_service_installation
    validate_service_enablement
    validate_pm2_integration
    validate_service_dependencies
    validate_health_checks
    validate_auto_restart_functionality
    
    # Generate test report
    log_header "Test Results Summary"
    
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    log_info "Total Tests: $total_tests"
    log_info "Passed: $TESTS_PASSED"
    log_info "Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_success "🎉 All systemd integration tests passed!"
        log_info ""
        log_info "✅ Systemd service is properly integrated and functional"
        log_info "✅ PM2 startup configuration is correct"
        log_info "✅ Service dependencies are working"
        log_info "✅ Health checks are passing"
        log_info "✅ Auto-restart capability is ready"
        log_info ""
        log_info "🚀 Production VPS is ready for automatic system startup!"
        
        echo "VALIDATION_STATUS=SUCCESS" >> "$TEST_LOG"
        exit 0
    else
        log_error "❌ $TESTS_FAILED systemd integration test(s) failed"
        log_info ""
        log_info "Failed tests:"
        for failed_test in "${FAILED_TESTS[@]}"; do
            log_error "  • $failed_test"
        done
        log_info ""
        
        if [[ "$ROLLBACK_ON_FAILURE" == "--rollback-on-failure" ]]; then
            log_warning "🔄 Initiating rollback procedures..."
            rollback_changes
        else
            log_info "🔧 Manual intervention required. Run with --rollback-on-failure to auto-rollback."
        fi
        
        echo "VALIDATION_STATUS=FAILED" >> "$TEST_LOG"
        echo "FAILED_TESTS=$TESTS_FAILED" >> "$TEST_LOG"
        exit 1
    fi
}

# =============================================================================
# ROLLBACK PROCEDURES
# =============================================================================
rollback_changes() {
    log_header "Rollback Procedures"
    
    log_info "🔄 Rolling back systemd service changes..."
    
    # Stop and disable service
    sudo systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    sudo systemctl disable "$SERVICE_NAME" 2>/dev/null || true
    
    # Remove service file
    if [[ -f "$SERVICE_FILE" ]]; then
        sudo rm -f "$SERVICE_FILE"
        log_info "Removed service file: $SERVICE_FILE"
    fi
    
    # Reload systemd daemon
    sudo systemctl daemon-reload
    
    log_success "✅ Rollback completed"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
