#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - PRODUCTION DEPLOYMENT VERIFICATION
# =============================================================================
# Version: 1.0.0
# Description: Quick verification script for production VPS deployment
# Usage: ./verify-production-deployment.sh
# 
# Target: Ubuntu 24.04 VPS at truckhaul.top (IP: **************)
# User: ubuntu
# 
# This script performs essential checks to ensure the production deployment
# is working correctly with systemd integration and PM2 auto-startup.
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================
SERVICE_NAME="hauling-qr-system"
APP_DIR="/home/<USER>/hauling-qr-trip-management"
HEALTH_URL="http://localhost:8080/api/health"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

# =============================================================================
# VERIFICATION FUNCTIONS
# =============================================================================
check_system_info() {
    log_header "System Information"
    
    echo "Hostname: $(hostname)"
    echo "OS: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Unknown')"
    echo "User: $(whoami)"
    echo "Date: $(date)"
    echo "Uptime: $(uptime -p 2>/dev/null || uptime)"
    echo ""
}

check_application_directory() {
    log_header "Application Directory Check"
    
    if [[ -d "$APP_DIR" ]]; then
        log_success "Application directory exists: $APP_DIR"
        
        # Check key files
        local key_files=("server/server.js" "ecosystem.config.js" ".env")
        for file in "${key_files[@]}"; do
            if [[ -f "$APP_DIR/$file" ]]; then
                log_success "Key file exists: $file"
            else
                log_error "Key file missing: $file"
            fi
        done
    else
        log_error "Application directory not found: $APP_DIR"
        return 1
    fi
}

check_systemd_service() {
    log_header "Systemd Service Check"
    
    # Check if service file exists
    local service_file="/etc/systemd/system/${SERVICE_NAME}.service"
    if [[ -f "$service_file" ]]; then
        log_success "Systemd service file exists"
    else
        log_error "Systemd service file not found: $service_file"
        return 1
    fi
    
    # Check if service is enabled
    if sudo systemctl is-enabled "$SERVICE_NAME" >/dev/null 2>&1; then
        log_success "Service is enabled for auto-start"
    else
        log_error "Service is not enabled"
    fi
    
    # Check service status
    local status=$(sudo systemctl is-active "$SERVICE_NAME" 2>/dev/null || echo "inactive")
    echo "Service status: $status"
    
    if [[ "$status" == "active" ]]; then
        log_success "Service is currently active"
    else
        log_warning "Service is not currently active"
    fi
}

check_pm2_configuration() {
    log_header "PM2 Configuration Check"
    
    # Check PM2 installation
    if command -v pm2 >/dev/null 2>&1; then
        log_success "PM2 is installed: $(pm2 -v)"
    else
        log_error "PM2 is not installed"
        return 1
    fi
    
    # Check PM2_HOME
    if [[ -d "/home/<USER>/.pm2" ]]; then
        log_success "PM2_HOME directory exists"
    else
        log_error "PM2_HOME directory not found"
    fi
    
    # Check PM2 startup configuration
    if pm2 startup 2>&1 | grep -q "already" || [[ -f "/etc/systemd/system/pm2-ubuntu.service" ]]; then
        log_success "PM2 startup is configured"
    else
        log_warning "PM2 startup may not be configured"
    fi
    
    # Check saved processes
    if [[ -f "/home/<USER>/.pm2/dump.pm2" ]]; then
        log_success "PM2 process list is saved"
    else
        log_warning "No saved PM2 processes found"
    fi
    
    # Check current PM2 processes
    local pm2_status=$(pm2 list 2>/dev/null || echo "PM2 not accessible")
    if echo "$pm2_status" | grep -q "hauling-qr-server"; then
        if echo "$pm2_status" | grep -q "online"; then
            log_success "Hauling QR server is running in PM2"
        else
            log_warning "Hauling QR server is in PM2 but not online"
        fi
    else
        log_warning "Hauling QR server not found in PM2 process list"
    fi
}

check_required_services() {
    log_header "Required Services Check"
    
    local required_services=("postgresql" "nginx")
    for service in "${required_services[@]}"; do
        if sudo systemctl is-active "$service" >/dev/null 2>&1; then
            log_success "$service is active"
        else
            log_error "$service is not active"
        fi
        
        if sudo systemctl is-enabled "$service" >/dev/null 2>&1; then
            log_success "$service is enabled"
        else
            log_error "$service is not enabled"
        fi
    done
}

check_application_health() {
    log_header "Application Health Check"
    
    # Test health endpoint
    log_info "Testing health endpoint: $HEALTH_URL"
    
    if curl -f -s "$HEALTH_URL" >/dev/null 2>&1; then
        log_success "Application health endpoint is responding"
        
        # Get health details
        local health_response=$(curl -s "$HEALTH_URL" 2>/dev/null || echo "{}")
        echo "Health response: $health_response"
    else
        log_error "Application health endpoint is not responding"
        log_info "This could indicate the application is not running or not accessible"
    fi
    
    # Test CORS functionality
    if curl -f -s -H "Origin: https://truckhaul.top" "http://localhost:8080/cors-test" >/dev/null 2>&1; then
        log_success "CORS functionality is working"
    else
        log_warning "CORS test failed (may be expected if application is not running)"
    fi
}

check_network_connectivity() {
    log_header "Network Connectivity Check"
    
    # Check if port 8080 is listening
    if netstat -tlnp 2>/dev/null | grep -q ":8080 "; then
        log_success "Port 8080 is listening"
    else
        log_warning "Port 8080 is not listening"
    fi
    
    # Check if port 80 is listening (NGINX)
    if netstat -tlnp 2>/dev/null | grep -q ":80 "; then
        log_success "Port 80 is listening (NGINX)"
    else
        log_warning "Port 80 is not listening"
    fi
    
    # Check if port 443 is listening (HTTPS)
    if netstat -tlnp 2>/dev/null | grep -q ":443 "; then
        log_success "Port 443 is listening (HTTPS)"
    else
        log_info "Port 443 is not listening (HTTPS may not be configured)"
    fi
}

# =============================================================================
# QUICK ACTIONS
# =============================================================================
show_management_commands() {
    log_header "Management Commands"
    
    echo "🔧 Systemd Service Management:"
    echo "   sudo systemctl start $SERVICE_NAME"
    echo "   sudo systemctl stop $SERVICE_NAME"
    echo "   sudo systemctl restart $SERVICE_NAME"
    echo "   sudo systemctl status $SERVICE_NAME"
    echo "   sudo journalctl -u $SERVICE_NAME -f"
    echo ""
    echo "🔧 PM2 Management:"
    echo "   pm2 list"
    echo "   pm2 logs hauling-qr-server"
    echo "   pm2 restart hauling-qr-server"
    echo "   pm2 reload hauling-qr-server"
    echo "   pm2 monit"
    echo ""
    echo "🔧 Health Checks:"
    echo "   curl http://localhost:8080/api/health"
    echo "   curl http://localhost:8080/api/health/db"
    echo "   curl -H 'Origin: https://truckhaul.top' http://localhost:8080/cors-test"
    echo ""
    echo "🔧 System Integration Test:"
    echo "   ./scripts/test-systemd-integration.sh --production"
    echo ""
}

# =============================================================================
# MAIN FUNCTION
# =============================================================================
main() {
    log_header "Production Deployment Verification"
    log_info "Verifying Hauling QR Trip System deployment on production VPS"
    log_info "Target: truckhaul.top (Ubuntu 24.04)"
    echo ""
    
    # Run all checks
    check_system_info
    check_application_directory
    check_systemd_service
    check_pm2_configuration
    check_required_services
    check_application_health
    check_network_connectivity
    show_management_commands
    
    log_header "Verification Complete"
    log_info "Review the results above to ensure all components are properly configured."
    log_info "If any issues are found, use the management commands provided to troubleshoot."
    log_info ""
    log_info "For comprehensive testing, run:"
    log_info "  ./scripts/test-systemd-integration.sh --production"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
