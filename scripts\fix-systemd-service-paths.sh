#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SYSTEMD SERVICE PATH FIX
# =============================================================================
# Version: 1.0.0
# Description: Fix systemd service paths to match actual deployment location
# Issue: Service template had incorrect paths pointing to wrong directories
# 
# This script updates the systemd service with correct paths:
# - WorkingDirectory: /var/www/hauling-qr-system (not /home/<USER>/hauling-qr-trip-management)
# - PM2 config paths: /var/www/hauling-qr-system/ecosystem.config.js
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICE_NAME="hauling-qr-system"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
TEMPLATE_FILE="$PROJECT_ROOT/deploy-hauling-qr-ubuntu/templates/${SERVICE_NAME}.service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================
validate_environment() {
    log_header "Environment Validation"
    
    # Check if template file exists
    if [[ ! -f "$TEMPLATE_FILE" ]]; then
        log_error "Template file not found: $TEMPLATE_FILE"
        return 1
    fi
    
    # Check if we have sudo access
    if ! sudo -n true 2>/dev/null; then
        log_error "This script requires sudo access"
        return 1
    fi
    
    # Check if actual deployment directory exists
    if [[ ! -d "/var/www/hauling-qr-system" ]]; then
        log_error "Deployment directory not found: /var/www/hauling-qr-system"
        log_info "Please verify the actual deployment location"
        return 1
    fi
    
    # Check if ecosystem.config.js exists in deployment directory
    if [[ ! -f "/var/www/hauling-qr-system/ecosystem.config.js" ]]; then
        log_error "PM2 config not found: /var/www/hauling-qr-system/ecosystem.config.js"
        return 1
    fi
    
    log_success "Environment validation passed"
    return 0
}

validate_template_paths() {
    log_header "Template Path Validation"
    
    # Check that template has correct WorkingDirectory
    if grep -q "WorkingDirectory=/var/www/hauling-qr-system" "$TEMPLATE_FILE"; then
        log_success "✅ Template has correct WorkingDirectory"
    else
        log_error "❌ Template has incorrect WorkingDirectory"
        log_info "Expected: WorkingDirectory=/var/www/hauling-qr-system"
        log_info "Current: $(grep "WorkingDirectory=" "$TEMPLATE_FILE" || echo "Not found")"
        return 1
    fi
    
    # Check that template has correct PM2 paths
    if grep -q "/var/www/hauling-qr-system/ecosystem.config.js" "$TEMPLATE_FILE"; then
        log_success "✅ Template has correct PM2 config paths"
    else
        log_error "❌ Template has incorrect PM2 config paths"
        return 1
    fi
    
    return 0
}

# =============================================================================
# SERVICE MANAGEMENT FUNCTIONS
# =============================================================================
stop_service_safely() {
    log_header "Stopping Service Safely"
    
    # Check if service is running
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "Service is active, stopping..."
        if sudo systemctl stop "$SERVICE_NAME"; then
            log_success "Service stopped successfully"
        else
            log_error "Failed to stop service"
            return 1
        fi
    else
        log_info "Service is not active"
    fi
    
    return 0
}

backup_current_service() {
    log_header "Creating Service Backup"
    
    if [[ -f "$SERVICE_FILE" ]]; then
        local backup_file="${SERVICE_FILE}.backup.$(date +%Y%m%d-%H%M%S)"
        
        if sudo cp "$SERVICE_FILE" "$backup_file"; then
            log_success "Service backed up to: $backup_file"
            echo "$backup_file"
            return 0
        else
            log_error "Failed to create service backup"
            return 1
        fi
    else
        log_warning "Service file does not exist: $SERVICE_FILE"
        return 0
    fi
}

install_updated_service() {
    log_header "Installing Updated Service"
    
    # Copy template to system location
    if sudo cp "$TEMPLATE_FILE" "$SERVICE_FILE"; then
        log_success "Service template installed"
    else
        log_error "Failed to install service template"
        return 1
    fi
    
    # Set correct permissions
    if sudo chmod 644 "$SERVICE_FILE" && sudo chown root:root "$SERVICE_FILE"; then
        log_success "Service permissions set correctly"
    else
        log_error "Failed to set service permissions"
        return 1
    fi
    
    # Reload systemd daemon
    if sudo systemctl daemon-reload; then
        log_success "Systemd daemon reloaded"
    else
        log_error "Failed to reload systemd daemon"
        return 1
    fi
    
    return 0
}

# =============================================================================
# VERIFICATION FUNCTIONS
# =============================================================================
verify_service_configuration() {
    log_header "Service Configuration Verification"
    
    # Check that systemd can parse the service
    if sudo systemctl status "$SERVICE_NAME" >/dev/null 2>&1 || [[ $? -eq 3 ]]; then
        log_success "✅ Systemd can parse service configuration"
    else
        log_error "❌ Systemd cannot parse service configuration"
        return 1
    fi
    
    # Verify paths in installed service
    if sudo grep -q "WorkingDirectory=/var/www/hauling-qr-system" "$SERVICE_FILE"; then
        log_success "✅ Installed service has correct WorkingDirectory"
    else
        log_error "❌ Installed service has incorrect WorkingDirectory"
        return 1
    fi
    
    if sudo grep -q "/var/www/hauling-qr-system/ecosystem.config.js" "$SERVICE_FILE"; then
        log_success "✅ Installed service has correct PM2 paths"
    else
        log_error "❌ Installed service has incorrect PM2 paths"
        return 1
    fi
    
    return 0
}

test_service_start() {
    log_header "Service Start Test"
    
    log_info "Attempting to start systemd service..."
    
    if sudo systemctl start "$SERVICE_NAME"; then
        log_success "Service started successfully"
        
        # Wait a moment for startup
        sleep 5
        
        # Check if service is active
        if systemctl is-active --quiet "$SERVICE_NAME"; then
            log_success "✅ Service is active and running"
            
            # Check if PM2 processes are running
            if sudo -u ubuntu pm2 list | grep -q "hauling-qr-server.*online"; then
                log_success "✅ PM2 processes are running"
            else
                log_warning "⚠️ PM2 processes not found (may take time to start)"
            fi
            
            # Test health endpoint
            sleep 10
            if curl -f -s http://localhost:8080/api/health >/dev/null; then
                log_success "✅ Application health endpoint responding"
            else
                log_warning "⚠️ Application health endpoint not responding yet"
            fi
            
        else
            log_error "❌ Service failed to stay active"
            log_info "Service logs:"
            sudo journalctl -u "$SERVICE_NAME" -n 10 --no-pager
            return 1
        fi
    else
        log_error "❌ Failed to start service"
        log_info "Service logs:"
        sudo journalctl -u "$SERVICE_NAME" -n 10 --no-pager
        return 1
    fi
    
    return 0
}

# =============================================================================
# MAIN EXECUTION FUNCTION
# =============================================================================
main() {
    log_header "Systemd Service Path Fix"
    log_info "Fixing systemd service paths to match actual deployment location"
    log_info "Service: $SERVICE_NAME"
    log_info "Template: $TEMPLATE_FILE"
    echo ""
    
    # Step 1: Environment validation
    if ! validate_environment; then
        log_error "Environment validation failed"
        exit 1
    fi
    
    # Step 2: Template validation
    if ! validate_template_paths; then
        log_error "Template path validation failed"
        log_info "Please ensure the template file has been updated with correct paths"
        exit 1
    fi
    
    # Step 3: Stop service safely
    if ! stop_service_safely; then
        log_error "Failed to stop service safely"
        exit 1
    fi
    
    # Step 4: Backup current service
    local backup_file
    backup_file=$(backup_current_service)
    if [[ $? -ne 0 ]]; then
        log_error "Service backup failed"
        exit 1
    fi
    
    # Step 5: Install updated service
    if ! install_updated_service; then
        log_error "Service installation failed"
        if [[ -n "$backup_file" && -f "$backup_file" ]]; then
            log_info "Restoring from backup: $backup_file"
            sudo cp "$backup_file" "$SERVICE_FILE"
            sudo systemctl daemon-reload
        fi
        exit 1
    fi
    
    # Step 6: Verify service configuration
    if ! verify_service_configuration; then
        log_error "Service configuration verification failed"
        exit 1
    fi
    
    # Step 7: Test service start
    if ! test_service_start; then
        log_error "Service start test failed"
        log_info "Service has been updated but may need manual troubleshooting"
        exit 1
    fi
    
    # Final summary
    log_header "Fix Summary"
    log_success "🎉 Systemd service path fix completed successfully!"
    log_info ""
    log_info "📊 Changes Applied:"
    log_info "   • WorkingDirectory: /var/www/hauling-qr-system"
    log_info "   • PM2 Config Path: /var/www/hauling-qr-system/ecosystem.config.js"
    log_info "   • Service reloaded and started"
    if [[ -n "$backup_file" ]]; then
        log_info "   • Backup created: $backup_file"
    fi
    log_info ""
    log_info "✅ Service Status:"
    log_info "   • Systemd service: $(systemctl is-active "$SERVICE_NAME")"
    log_info "   • Service enabled: $(systemctl is-enabled "$SERVICE_NAME")"
    log_info ""
    log_info "🔧 Management Commands:"
    log_info "   • Check status: sudo systemctl status $SERVICE_NAME"
    log_info "   • View logs: sudo journalctl -u $SERVICE_NAME -f"
    log_info "   • Restart: sudo systemctl restart $SERVICE_NAME"
    log_info ""
    log_info "🧪 Verification:"
    log_info "   • Run: ./scripts/verify-production-deployment.sh"
    log_info "   • Test: ./scripts/test-systemd-integration.sh --production"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
