[Unit]
Description=Hauling QR Trip Management System
Documentation=https://github.com/mightybadz18/hauling-qr-trip-management
After=network.target postgresql.service nginx.service
Wants=postgresql.service nginx.service
Requires=network.target

[Service]
Type=forking
User=ubuntu
Group=ubuntu
WorkingDirectory=/var/www/hauling-qr-system
Environment=NODE_ENV=production
Environment=PATH=/usr/bin:/usr/local/bin:/home/<USER>/.nvm/versions/node/v20.18.0/bin:/usr/local/bin
Environment=NVM_DIR=/home/<USER>/.nvm
Environment=PM2_HOME=/home/<USER>/.pm2

# Pre-start checks
ExecStartPre=/bin/bash -c 'echo "Starting Hauling QR Trip System..."'
ExecStartPre=/bin/bash -c 'systemctl is-active --quiet postgresql || (echo "PostgreSQL not running" && exit 1)'
ExecStartPre=/bin/bash -c 'systemctl is-active --quiet nginx || (echo "NGINX not running" && exit 1)'
ExecStartPre=/bin/bash -c 'pg_isready -h localhost -p 5432 -U hauling_app -d hauling_qr_system || (echo "Database not accessible" && exit 1)'

# Start command - Use shell to source NVM and find PM2
ExecStart=/bin/bash -c 'source /home/<USER>/.nvm/nvm.sh && pm2 start /var/www/hauling-qr-system/ecosystem.config.js --env production --no-daemon'

# Reload command
ExecReload=/bin/bash -c 'source /home/<USER>/.nvm/nvm.sh && pm2 reload /var/www/hauling-qr-system/ecosystem.config.js --env production'

# Stop command
ExecStop=/bin/bash -c 'source /home/<USER>/.nvm/nvm.sh && pm2 stop all'
ExecStop=/bin/bash -c 'source /home/<USER>/.nvm/nvm.sh && pm2 delete all'

# Health check
ExecStartPost=/bin/bash -c 'sleep 10 && curl -f http://localhost:8080/api/health || (echo "Health check failed" && exit 1)'

# Restart configuration
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/hauling-qr-system /home/<USER>/.pm2 /tmp

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=hauling-qr-system

[Install]
WantedBy=multi-user.target
